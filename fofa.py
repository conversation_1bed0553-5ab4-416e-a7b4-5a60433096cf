import json
import requests
import random
import re
import base64
import urllib3
from concurrent.futures import ThreadPoolExecutor

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class vul:

    def __init__(self):
        self.fofa_email = '<EMAIL>'
        self.fofa_key = 'e0042ab6af33378020c6bae556357545'
        self.urls = []

        self.ramdom = ''
        for i in random.sample('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890', 15):
            self.ramdom += i
        self.proxy = {"http": "127.0.0.1:8080", "https": "127.0.0.1:8080"}
        self.executor = ThreadPoolExecutor(max_workers=20)

    def fofa(self, dork):
        keyword = str(base64.b64encode(dork.encode()), encoding="utf8")
        url = 'https://fofa.info/api/v1/search/all?email={}&key={}&qbase64={}&size=10000'.format(self.fofa_email, self.fofa_key, keyword)
        try:
            response = requests.get(url,verify=False)  # 获取网页源代码内容
            response.raise_for_status()  # 失败请求(非200响应)抛出异常
            response.encoding = "utf-8"  # 设置网页编码
            fofa_data = response.json()
            print(len(fofa_data['results']))
            for data in fofa_data['results']:
                url = data[0]
                if url[-4:] == ':443' and 'https://' not in url:
                    url = 'https://' + url
                elif not re.findall('http://', url) and  not re.findall('https://', url):
                    url = 'http://' + url
                url+='/'
                self.urls.append(url)
                with open('result.txt', 'a') as f:
                    f.write(url + '\n')
        except:
            print('请求错误，请检查网络情况！')


if __name__ == "__main__":
    a = vul()
    a.fofa('''title="Letta" || body="Letta Platform" || header="X-Letta-Version"''')
# a.Thread(a.Nginx_status)
