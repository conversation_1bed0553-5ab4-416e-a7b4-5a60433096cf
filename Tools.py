# coding:utf-8
import time
import json
import requests
import urllib3
import random
import hashlib
import base64
import re
import urllib.parse
import datetime
import zoneinfo

urllib3.disable_warnings()


class Tools:
    def __init__(self, dnslog_url):
        self.dnslog_url = dnslog_url
        self._ua_list = UA.split('\n')

    # 随机UA
    def random_agent(self):
        return self._ua_list[random.randint(0, len(self._ua_list) - 1)]

    # 获得指定长度的随机字符串
    def random_str(self, num):
        str = ''
        for i in random.sample('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890', int(num)):
            str += i
        return str

    # base64 编码
    def b64encode(self, string):
        a = base64.b64encode(string.encode())
        return a.decode()

    # base64 解码
    def b64decode(self, string):
        a = base64.b64decode(string).decode()
        return a

    # url全编码和标准编码(切勿将http://xxx/部分带入编码，否则无法访问)
    def url_encode(self, string, m):
        if m == 'all':
            encode_string = ""
            for char in string:
                encode_char = hex(ord(char)).replace("0x", "%")
                encode_string += encode_char
        else:
            encode_string = urllib.parse.quote(string)
        return encode_string

    # url解码
    def url_decode(self, string):
        return urllib.parse.unquote(string)

    # md5 加密
    def md5_encode(self, text, length):
        md5 = hashlib.md5()
        md5.update(text.encode('utf-8'))
        encrypted = md5.hexdigest()
        if length == 16:
            return encrypted[8:24]
        else:
            return encrypted

    # 处理url路径：当前目录/根目录/host:port/二级目录/host
    def deal_path(self, url, m):
        if m == 'base':
            base_url = re.sub(r'#.*|\?.*', '', url)
            while base_url[-1] != '/':
                base_url = base_url[:-1]
            return base_url
        elif m == 'root':
            root_url = url
            while root_url.count('/') > 3:
                root_url = root_url[:-1]
            while root_url[-1] != '/':
                root_url = root_url[:-1]
            return root_url
        elif m == 'host_port':
            host_port_tmp = url
            while host_port_tmp.count('/') > 3:
                host_port_tmp = host_port_tmp[:-1]
            while host_port_tmp[-1] != '/':
                host_port_tmp = host_port_tmp[:-1]
            host_port_tmp = host_port_tmp.replace('https://', '').replace('http://', '').replace('/', '')
            if 'https://' in url and ':' not in host_port_tmp:
                host_port = f'{host_port_tmp}:443'
            elif 'http://' in url and ':' not in host_port_tmp:
                host_port = f'{host_port_tmp}:80'
            else:
                host_port = host_port_tmp
            return host_port
        elif m == 'sub':
            sub_url = url
            while sub_url.count('/') > 4:
                sub_url = sub_url[:-1]
            while sub_url[-1] != '/':
                sub_url = sub_url[:-1]
            return sub_url
        else:
            url = url.replace('https://', '').replace('http://', '')
            url = re.sub(r'/.*', '', url)
            host = re.sub(r':.*', '', url)
            return host

    # 绘制请求包，返回base64后的结果
    def get_req_custom(self, method, url, headers, body):
        if type(body) == dict:
            body = json.dumps(body)
        method = method.upper()
        uri = url.replace(self.deal_path(url, 'root'), '')
        if self.deal_path(url, 'host_port')[1] in ['80', '443']:
            host = self.deal_path(url, 'host')
        else:
            host = self.deal_path(url, 'host_port')
        headers = ''.join(header + '\n' for header in ["{}: {}".format(key, headers[key]) for key in headers.keys()])
        req = self.b64encode('''{} /{} HTTP/1.1\nHost: {}\n{}\n{}'''.format(method, uri, host, headers, body))
        return req

    # 绘制请求包，返回base64后的结果
    def get_req(self, res):
        method = res.request.method
        uri = res.request.url.replace(self.deal_path(res.request.url, 'root'), '')
        if self.deal_path(res.request.url, 'host_port')[1] in ['80', '443']:
            host = self.deal_path(res.request.url, 'host')
        else:
            host = self.deal_path(res.request.url, 'host_port')
        headers = ''.join(header + '\n' for header in ["{}: {}".format(key, res.request.headers[key]) for key in res.request.headers.keys()])
        if res.request.body is not None:
            if type(res.request.body) == bytes:
                try:
                    body = res.request.body.decode()
                except:
                    body = res.request.body
            else:
                body = res.request.body
        else:
            body = ''
        req = self.b64encode('''{} /{} HTTP/1.1\nHost: {}\n{}\n{}'''.format(method, uri, host, headers, body))
        return req

    # 绘制响应包，返回base64后的结果
    # omit参数为是否省略，默认取全部响应。对于大响应，但是只取部分结果也能证明漏洞存在的情况，可以通过该参数截断。
    def get_res(self, res, omit=False):
        res_headers = ''
        for key in res.headers:
            res_headers += key + ': ' + res.headers[key] + '\n'
        if omit:
            body = res.text[:3000] + '\n省略...\n'
        else:
            body = res.text
        response = '''HTTP/1.1 {} {}\n{}\n{}'''.format(res.status_code, res.reason, res_headers, body)
        return self.b64encode(response)

    # 只绘制响应包中的headers，返回base64后的结果
    def get_res_headers(self, res):
        res_headers = ''
        for key in res.headers:
            res_headers += key + ': ' + res.headers[key] + '\n'
        response = '''HTTP/1.1 {} {}\n{}'''.format(res.status_code, res.reason, res_headers)
        return self.b64encode(response)

    # 获取dnslog
    def dns_get(self):
        try:
            res = requests.post(url=f'{self.dnslog_url}/dns_get', json={'auth': '[Ljava.lang.String;@569dd1fb'}).json()
        except:
            res = {'domain': 'baidu.com', 'key': '401', 'token': '401', 'uid': '401'}
        return res

    # 检查dnslog
    def dns_check(self, dns):
        time.sleep(6)
        try:
            dns_res = requests.post(url=f'{self.dnslog_url}/dns_check', json={'auth': '[Ljava.lang.String;@569dd1fb', 'dnslog_result': dns}).json()
            if dns_res['res'] == 'true':
                return True, dns_res['text']
            else:
                return False, 'null'
        except:
            return False, 'null'

    # 单纯获取dnslog结果，不检查是否命中，适用于模糊测试
    def dns_res_get(self, dns):
        time.sleep(6)
        try:
            dns_res = requests.post(url=f'{self.dnslog_url}/CheckDnslogResult', json={'auth': '[Ljava.lang.String;@569dd1fb', 'dnslog_info': dns}).json()
            return str(dns_res)
        except:
            return None

    def random_proxy(self, proxy_list):
        if proxy_list:
            proxy = random.choice(proxy_list).replace('http://', '').replace('socks5', 'socks5h')
            if proxy[-1] == '/':
                proxy = proxy[:-1]
            result = {'http': proxy, 'https': proxy}
            return result
        else:
            return None

    def random_proxy_asyncio(self, proxy_list):
        if proxy_list:
            return random.choice(proxy_list)
        else:
            return None

    # 随机生成ip地址，用于伪造xff
    def fake_ip(self):
        ip = []
        for i in range(4):
            ip.append(str(random.randint(0, 255)))
        return '.'.join(ip)

    # 获取当前时间
    def get_ctime(self):
        ctime = datetime.datetime.now(zoneinfo.ZoneInfo('Asia/Shanghai')).strftime('%Y-%m-%d %H:%M:%S')
        return ctime


UA = '''Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; zh-cn) Opera 8.65
Mozilla/5.0 (compatible; MSIE 6.0; Windows NT 5.1; zh-cn) Opera 8.65
Mozilla/5.0 (Windows NT 5.1; U; zh-cn; rv:1.8.1) Gecko/20061208 Firefox/2.0.0 Opera 9.50
Mozilla/5.0 (Windows NT 5.1; U; zh-cn; rv:*******) Gecko/20091201 Firefox/3.5.6 Opera 10.53
Mozilla/5.0 (Windows NT 5.1; U; zh-cn; rv:*******) Gecko/20091201 Firefox/3.5.6 Opera 10.70
Opera/8.50 (Windows NT 4.0; U; zh-cn)
Opera/8.54 (Windows NT 4.0; U; zh-cn)
Opera/9.02 (Windows NT 5.1; U; zh-cn)
Opera/9.23 (Windows NT 5.1; U; zh-cn)
Opera/9.25 (Windows NT 5.1; U; zh-cn)
Opera/9.26 (Windows NT 5.1; U; zh-cn)
Opera/9.61 (Windows NT 5.1; U; zh-cn) Presto/2.1.1
Opera/9.62 (Windows NT 5.1; U; zh-cn) Presto/2.1.1
Opera/9.64 (Windows NT 6.0; U; zh-cn) Presto/2.1.1
Opera/9.80 (Windows NT 5.1; U; zh-cn) Presto/2.2.15 Version/10.00
Opera/9.80 (Windows NT 5.2; U; zh-cn) Presto/2.6.30 Version/10.63
Opera/9.80 (Windows NT 6.0; U; zh-cn) Presto/2.5.22 Version/10.50
Opera/9.80 (Windows NT 6.1; U; zh-cn) Presto/2.2.15 Version/10.00
Opera/9.80 (Windows NT 6.1; U; zh-cn) Presto/2.5.22 Version/10.50
Opera/9.80 (Windows NT 6.1; U; zh-cn) Presto/2.6.30 Version/10.61
Opera/9.80 (Windows NT 6.1; U; zh-cn) Presto/2.6.37 Version/11.00
Opera/9.80 (Windows NT 6.1; U; zh-cn) Presto/2.7.62 Version/11.01
Opera/10.60 (Windows NT 5.1; U; zh-cn) Presto/2.6.30 Version/10.60
Mozilla/5.0 (Windows NT 5.1; U; zh-cn; rv:1.8.1) Gecko/20091102 Firefox/3.5.5
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.8.0.9) Gecko/20061206 Firefox/1.5.0.9
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.8.1.16) Gecko/20080702 Firefox/2.0.0.17
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.8.1.18) Gecko/20081029 Firefox/2.0.0.18
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.8.1.20) Gecko/20081217 Firefox/2.0.0.20
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.8.1.9) Gecko/20071025 Firefox/2.0.0.9
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9.1b4) Gecko/20090423 Firefox/3.5b4
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9.1b4) Gecko/20090423 Firefox/3.5b4 (.NET CLR 3.5.30729)
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9.2.4) Gecko/20100503 Firefox/3.6.4 (.NET CLR 3.5.30729)
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9.2.4) Gecko/20100513 Firefox/3.6.4 (.NET CLR 3.5.30729)
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9.2.8) Gecko/20100722 Firefox/3.6.8
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9b3) Gecko/2008020514 Firefox/3.0b3
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN; rv:1.9b4) Gecko/2008030714 Firefox/3.0b4
Mozilla/5.0 (Windows; U; Windows NT 5.2; zh-CN; rv:1.9.1.5) Gecko/Firefox/3.5.5
Mozilla/5.0 (Windows; U; Windows NT 6.0; zh-CN; rv:1.8.1.20) Gecko/20081217 Firefox/2.0.0.19
Mozilla/5.0 (Windows; U; Windows NT 6.0; zh-CN; rv:1.9.0.19) Gecko/2010031422 Firefox/3.0.19 (.NET CLR 3.5.30729)
Mozilla/5.0 (Windows; U; Windows NT 6.0; zh-CN; rv:1.9.2.4) Gecko/20100513 Firefox/3.6.4
Mozilla/5.0 (Windows; U; Windows NT 6.0; zh-CN; rv:1.9.2.6) Gecko/20100625 Firefox/3.6.6 GTB7.1
Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:1.9.1.2) Gecko/20090729 Firefox/3.5.2 (.NET CLR 3.5.30729)
Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:1.9.1.3) Gecko/20090824 Firefox/3.5.3
Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:1.9.2.12) Gecko/20101026 Firefox/3.6.12 (.NET CLR 3.5.30729; .NET4.0E)
Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:1.9.2.14) Gecko/20110218 Firefox/3.6.14
Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:1.9.2.3) Gecko/20100401 Firefox/3.6.3 (.NET CLR 3.5.30729)
Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:1.9.2.8) Gecko/20100722 Firefox/3.6.8
Mozilla/5.0 (X11; U; Linux i686; zh-CN; rv:*******) Gecko/20091216 Fedora/3.5.6-1.fc11 Firefox/3.5.6 GTB6
Mozilla/5.0 (X11; U; Linux i686; zh-CN; rv:1.9.1.8) Gecko/20100216 Fedora/3.5.8-1.fc12 Firefox/3.5.8
Mozilla/5.0 (X11; U; Linux i686; zh-CN; rv:1.9.2.8) Gecko/20100722 Ubuntu/10.04 (lucid) Firefox/3.6.8
Mozilla/5.0 (X11; U; Linux x86_64; zh-CN; rv:1.9.2.10) Gecko/20100922 Ubuntu/10.10 (maverick) Firefox/3.6.10
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN) AppleWebKit/533.16 (KHTML, like Gecko) Chrome/5.0.335.0 Safari/533.16
Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_5_8; zh-cn) AppleWebKit/533.18.1 (KHTML, like Gecko) Version/5.0.2 Safari/533.18.5
Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_3; zh-cn) AppleWebKit/533.16 (KHTML, like Gecko) Version/5.0 Safari/533.16
Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_6; zh-cn) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27
Mozilla/5.0 (Macintosh; U; PPC Mac OS X 10_5_8; zh-cn) AppleWebKit/533.20.25 (KHTML, like Gecko) Version/5.0.4 Safari/533.20.27
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN) AppleWebKit/528.16 (KHTML, like Gecko) Version/4.0 Safari/528.16
Mozilla/5.0 (Windows; U; Windows NT 5.1; zh-CN) AppleWebKit/530.19.2 (KHTML, like Gecko) Version/4.0.2 Safari/530.19.1
Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN) AppleWebKit/533+ (KHTML, like Gecko)'''